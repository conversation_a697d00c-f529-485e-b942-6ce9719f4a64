import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { WorkflowBuilder } from '../../components/automation/workflow-builder';
import { WorkflowEngine, Workflow, WorkflowNode } from '../../lib/automation/workflow-engine';

// Mock the workflow engine
jest.mock('../../lib/automation/workflow-engine');

const MockedWorkflowEngine = WorkflowEngine as jest.MockedClass<typeof WorkflowEngine>;

// Mock React Flow
jest.mock('reactflow', () => ({
  ReactFlow: ({ children, nodes, edges, onNodesChange, onEdgesChange, onConnect }: any) => (
    <div data-testid="react-flow">
      <div data-testid="nodes">{nodes?.length || 0} nodes</div>
      <div data-testid="edges">{edges?.length || 0} edges</div>
      {children}
    </div>
  ),
  Controls: () => <div data-testid="flow-controls">Controls</div>,
  MiniMap: () => <div data-testid="minimap">MiniMap</div>,
  Background: () => <div data-testid="background">Background</div>,
  addEdge: jest.fn((edge, edges) => [...edges, edge]),
  useNodesState: jest.fn(() => [[], jest.fn(), jest.fn()]),
  useEdgesState: jest.fn(() => [[], jest.fn(), jest.fn()]),
  useReactFlow: jest.fn(() => ({
    getNodes: jest.fn(() => []),
    getEdges: jest.fn(() => []),
    setNodes: jest.fn(),
    setEdges: jest.fn()
  }))
}));

// Mock data
const mockWorkflow: Workflow = {
  id: 'workflow1',
  name: 'Test Workflow',
  description: 'A test workflow',
  version: '1.0.0',
  author: 'User',
  created: new Date(),
  modified: new Date(),
  tags: [],
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger',
      name: 'Time Trigger',
      description: 'Triggers workflow at specified time',
      position: { x: 100, y: 100 },
      config: {
        schedule: '20:00',
        enabled: true
      },
      enabled: true
    }
  ],
  connections: [],
  variables: [],
  triggers: [],
  enabled: true,
  executionCount: 0
};

const mockOnSave = jest.fn();
const mockOnExecute = jest.fn();

describe('WorkflowBuilder', () => {
  let mockEngine: jest.Mocked<WorkflowEngine>;

  beforeEach(() => {
    mockEngine = {
      validateWorkflow: jest.fn().mockReturnValue({ isValid: true, errors: [], warnings: [] }),
      executeWorkflow: jest.fn().mockResolvedValue({
        id: 'exec1',
        workflowId: 'workflow1',
        status: 'completed',
        startTime: new Date(),
        endTime: new Date(),
        steps: []
      }),
      calculateExecutionOrder: jest.fn().mockReturnValue(['trigger1']),
      on: jest.fn(),
      off: jest.fn()
    } as any;

    MockedWorkflowEngine.mockImplementation(() => mockEngine);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the workflow builder interface', () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      expect(screen.getByText('Node Palette')).toBeInTheDocument();
      expect(screen.getByText('Properties')).toBeInTheDocument();
    });

    it('should display workflow name and description', () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      expect(screen.getByDisplayValue('Test Workflow')).toBeInTheDocument();
      expect(screen.getByText('1 nodes, 0 connections')).toBeInTheDocument();
    });

    it('should show node palette', () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      expect(screen.getByText('Node Palette')).toBeInTheDocument();
      // The actual component shows node templates, not categories
      expect(screen.getByText('Trigger')).toBeInTheDocument();
      expect(screen.getByText('Equipment Control')).toBeInTheDocument();
      expect(screen.getByText('Capture Images')).toBeInTheDocument();
    });

    it('should display action buttons', () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /execute/i })).toBeInTheDocument();
    });
  });

  describe('Node Palette Interactions', () => {
    it('should show node templates in palette', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      // Check that node templates are visible
      expect(screen.getByText('Trigger')).toBeInTheDocument();
      expect(screen.getByText('Equipment Control')).toBeInTheDocument();
      expect(screen.getByText('Capture Images')).toBeInTheDocument();
      expect(screen.getByText('Process Images')).toBeInTheDocument();
    });

    it('should show node descriptions', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      // Check that node descriptions are visible
      expect(screen.getByText('Start the workflow')).toBeInTheDocument();
      expect(screen.getByText('Control telescope, camera, or other equipment')).toBeInTheDocument();
      expect(screen.getByText('Take photos with specified settings')).toBeInTheDocument();
    });

    it('should support drag and drop from palette', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      const equipmentControl = screen.getByText('Equipment Control');
      const canvas = screen.getByRole('tabpanel'); // The design tab content

      // Simulate drag and drop
      fireEvent.dragStart(equipmentControl);
      fireEvent.dragOver(canvas);
      fireEvent.drop(canvas);

      // Should add a new node to the workflow (check the node count in the toolbar)
      await waitFor(() => {
        expect(screen.getByText('2 nodes, 0 connections')).toBeInTheDocument();
      });
    });
  });

  describe('Node Configuration', () => {
    it('should show properties panel', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      // Should show the properties panel
      expect(screen.getByText('Properties')).toBeInTheDocument();
      expect(screen.getByText('Select a node to edit its properties')).toBeInTheDocument();
    });

    it('should show node configuration when node exists', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      // The workflow has a trigger node, so we should see some configuration
      expect(screen.getByText('Properties')).toBeInTheDocument();
    });

    it('should allow updating workflow name', async () => {
      const user = userEvent.setup();

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      // Find and update workflow name
      const nameInput = screen.getByDisplayValue('Test Workflow');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Workflow');

      expect(nameInput).toHaveValue('Updated Workflow');
    });

    it('should show workflow statistics', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onExecute={mockOnExecute}
        />
      );

      // Should show node and connection count
      expect(screen.getByText('1 nodes, 0 connections')).toBeInTheDocument();
    });
  });

  describe('Connection Management', () => {
    it('should allow connecting nodes', async () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Simulate connection creation (would normally happen through React Flow)
      // This would trigger the onConnect callback
      expect(screen.getByTestId('edges')).toHaveTextContent('0 edges');
    });

    it('should validate connections', async () => {
      mockEngine.validateWorkflow.mockReturnValue({
        isValid: false,
        errors: ['Invalid connection: trigger cannot connect to trigger'],
        warnings: []
      });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Should show validation errors
      expect(screen.getByText(/invalid connection/i)).toBeInTheDocument();
    });

    it('should allow deleting connections', async () => {
      const workflowWithConnection = {
        ...mockWorkflow,
        connections: [{
          id: 'conn1',
          source: 'trigger1',
          target: 'equipment1',
          sourceHandle: 'output',
          targetHandle: 'input'
        }]
      };

      render(
        <WorkflowBuilder
          workflow={workflowWithConnection}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Should show the connection
      expect(screen.getByTestId('edges')).toHaveTextContent('1 edges');
    });
  });

  describe('Workflow Validation', () => {
    it('should validate workflow on demand', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /validate/i }));

      expect(mockEngine.validateWorkflow).toHaveBeenCalledWith(mockWorkflow);
      expect(screen.getByText(/workflow is valid/i)).toBeInTheDocument();
    });

    it('should show validation errors', async () => {
      const user = userEvent.setup();
      
      mockEngine.validateWorkflow.mockReturnValue({
        isValid: false,
        errors: ['Workflow must have at least one trigger node'],
        warnings: ['Node is not connected']
      });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /validate/i }));

      expect(screen.getByText(/workflow must have at least one trigger/i)).toBeInTheDocument();
      expect(screen.getByText(/node is not connected/i)).toBeInTheDocument();
    });

    it('should validate automatically on changes', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Make a change
      const nameInput = screen.getByDisplayValue('Test Workflow');
      await user.type(nameInput, ' Updated');

      // Should trigger automatic validation
      await waitFor(() => {
        expect(mockEngine.validateWorkflow).toHaveBeenCalledTimes(2); // Initial + after change
      });
    });
  });

  describe('Workflow Testing', () => {
    it('should test workflow execution', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /test/i }));

      expect(mockOnTest).toHaveBeenCalledWith(mockWorkflow);
    });

    it('should show test execution results', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Should show test results panel
      expect(screen.getByText(/test results/i)).toBeInTheDocument();
    });

    it('should handle test failures', async () => {
      const user = userEvent.setup();
      
      mockEngine.executeWorkflow.mockResolvedValue({
        id: 'exec1',
        workflowId: 'workflow1',
        status: 'failed',
        startTime: new Date(),
        endTime: new Date(),
        steps: [],
        error: 'Test execution failed'
      });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /test/i }));

      await waitFor(() => {
        expect(screen.getByText(/test execution failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Variable Management', () => {
    it('should display workflow variables', () => {
      const workflowWithVariables = {
        ...mockWorkflow,
        variables: {
          targetName: 'M31',
          exposureTime: 300
        }
      };

      render(
        <WorkflowBuilder
          workflow={workflowWithVariables}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      expect(screen.getByText('Variables')).toBeInTheDocument();
      expect(screen.getByDisplayValue('targetName')).toBeInTheDocument();
      expect(screen.getByDisplayValue('M31')).toBeInTheDocument();
    });

    it('should allow adding new variables', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      const addVariableButton = screen.getByRole('button', { name: /add variable/i });
      await user.click(addVariableButton);

      expect(screen.getByPlaceholderText(/variable name/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/variable value/i)).toBeInTheDocument();
    });

    it('should allow editing variables', async () => {
      const user = userEvent.setup();
      
      const workflowWithVariables = {
        ...mockWorkflow,
        variables: {
          targetName: 'M31'
        }
      };

      render(
        <WorkflowBuilder
          workflow={workflowWithVariables}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      const valueInput = screen.getByDisplayValue('M31');
      await user.clear(valueInput);
      await user.type(valueInput, 'M42');

      expect(valueInput).toHaveValue('M42');
    });

    it('should allow deleting variables', async () => {
      const user = userEvent.setup();
      
      const workflowWithVariables = {
        ...mockWorkflow,
        variables: {
          targetName: 'M31',
          exposureTime: 300
        }
      };

      render(
        <WorkflowBuilder
          workflow={workflowWithVariables}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      const deleteButton = screen.getAllByRole('button', { name: /delete variable/i })[0];
      await user.click(deleteButton);

      // Should remove the variable
      expect(screen.queryByDisplayValue('targetName')).not.toBeInTheDocument();
    });
  });

  describe('Save and Cancel Operations', () => {
    it('should save workflow changes', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Make a change
      const nameInput = screen.getByDisplayValue('Test Workflow');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Workflow');

      await user.click(screen.getByRole('button', { name: /save/i }));

      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Updated Workflow'
        })
      );
    });

    it('should prevent saving invalid workflows', async () => {
      const user = userEvent.setup();
      
      mockEngine.validateWorkflow.mockReturnValue({
        isValid: false,
        errors: ['Invalid workflow'],
        warnings: []
      });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /save/i }));

      expect(mockOnSave).not.toHaveBeenCalled();
      expect(screen.getByText(/cannot save invalid workflow/i)).toBeInTheDocument();
    });

    it('should handle cancel operation', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /cancel/i }));

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('should show confirmation dialog for unsaved changes', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Make a change
      const nameInput = screen.getByDisplayValue('Test Workflow');
      await user.type(nameInput, ' Modified');

      // Try to cancel
      await user.click(screen.getByRole('button', { name: /cancel/i }));

      expect(screen.getByText(/unsaved changes/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /discard changes/i })).toBeInTheDocument();
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should support save shortcut', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.keyboard('{Control>}s{/Control}');

      expect(mockOnSave).toHaveBeenCalled();
    });

    it('should support undo/redo shortcuts', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Make a change
      const nameInput = screen.getByDisplayValue('Test Workflow');
      await user.type(nameInput, ' Modified');

      // Undo
      await user.keyboard('{Control>}z{/Control}');

      expect(nameInput).toHaveValue('Test Workflow');
    });

    it('should support delete shortcut for selected nodes', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Select a node (simulated)
      await user.keyboard('{Delete}');

      // Should remove the selected node
      expect(screen.getByTestId('nodes')).toHaveTextContent('0 nodes');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Workflow builder');
      expect(screen.getByRole('complementary')).toHaveAttribute('aria-label', 'Node palette');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Should be able to navigate between elements
      await user.tab();
      expect(screen.getByDisplayValue('Test Workflow')).toHaveFocus();
    });

    it('should announce important changes to screen readers', async () => {
      const user = userEvent.setup();
      
      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /validate/i }));

      expect(screen.getByRole('status')).toHaveTextContent(/workflow validated/i);
    });
  });

  describe('Error Handling', () => {
    it('should handle workflow engine errors', () => {
      mockEngine.validateWorkflow.mockImplementation(() => {
        throw new Error('Engine error');
      });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      expect(screen.getByText(/error loading workflow/i)).toBeInTheDocument();
    });

    it('should handle save errors gracefully', async () => {
      const user = userEvent.setup();
      
      mockOnSave.mockImplementation(() => {
        throw new Error('Save failed');
      });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      await user.click(screen.getByRole('button', { name: /save/i }));

      expect(screen.getByText(/error saving workflow/i)).toBeInTheDocument();
    });

    it('should recover from temporary errors', async () => {
      const user = userEvent.setup();
      
      // First call fails, second succeeds
      mockEngine.validateWorkflow
        .mockImplementationOnce(() => { throw new Error('Temporary error'); })
        .mockReturnValue({ isValid: true, errors: [], warnings: [] });

      render(
        <WorkflowBuilder
          workflow={mockWorkflow}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          onTest={mockOnTest}
        />
      );

      // Should show error initially
      expect(screen.getByText(/error loading workflow/i)).toBeInTheDocument();

      // Retry should work
      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);

      expect(screen.queryByText(/error loading workflow/i)).not.toBeInTheDocument();
    });
  });
});
