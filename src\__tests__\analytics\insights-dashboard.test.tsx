import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { InsightsDashboard } from '../../components/analytics/insights-dashboard';
import { SessionAnalyzer, ImagingSession } from '../../lib/analytics/session-analyzer';
import { EquipmentProfile } from '../../lib/stores/equipment-store';

// Mock the session analyzer
jest.mock('../../lib/analytics/session-analyzer');

const MockedSessionAnalyzer = SessionAnalyzer as jest.MockedClass<typeof SessionAnalyzer>;

// Mock data
const mockEquipment: EquipmentProfile[] = [
  {
    id: 'eq1',
    name: 'Test Setup',
    telescope: {
      model: 'Celestron EdgeHD 8',
      aperture: 203,
      focalLength: 2032,
      focalRatio: 10
    },
    camera: {
      model: 'ZWO ASI2600MC',
      pixelSize: 3.76,
      resolution: { width: 6248, height: 4176 },
      cooled: true
    },
    mount: {
      model: 'Celestron CGX',
      payload: 25,
      goto: true,
      tracking: true
    },
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      elevation: 10,
      timezone: 'America/New_York'
    }
  }
];

const mockSessions: ImagingSession[] = [
  {
    id: 'session1',
    date: new Date('2024-03-01T20:00:00Z'),
    duration: 240,
    target: {
      id: 'M31',
      name: 'Andromeda Galaxy',
      type: 'galaxy',
      coordinates: { ra: 10.6847, dec: 41.2687 },
      magnitude: 3.4
    },
    equipment: mockEquipment[0],
    conditions: {
      temperature: 15,
      humidity: 45,
      windSpeed: 8,
      cloudCover: 10,
      seeing: 3.5,
      transparency: 8,
      moonPhase: 0.25
    },
    images: [],
    statistics: {
      totalFrames: 48,
      acceptedFrames: 45,
      rejectedFrames: 3,
      totalIntegration: 225,
      averageHFR: 2.2,
      averageSNR: 43.5,
      guideRMS: 0.8,
      driftRate: 0.2
    },
    issues: [],
    notes: 'Good session overall'
  }
];

const mockMetrics = {
  totalSessions: 10,
  totalImagingTime: 2400, // minutes
  averageSessionDuration: 240,
  totalFrames: 480,
  acceptedFrames: 432,
  rejectedFrames: 48,
  averageHFR: 2.3,
  averageSNR: 42.1,
  averageGuideRMS: 0.9,
  efficiency: 90,
  integrationTime: 2160,
  frameAcceptanceRate: 90,
  equipmentUsage: {
    'Test Setup': 10
  },
  targetTypes: {
    galaxy: 6,
    nebula: 4
  },
  monthlyStats: {
    '2024-03': { sessions: 5, hours: 20 },
    '2024-02': { sessions: 3, hours: 12 },
    '2024-01': { sessions: 2, hours: 8 }
  }
};

const mockInsights = [
  {
    id: 'insight1',
    type: 'equipment' as const,
    title: 'Excellent Focus Performance',
    description: 'Your average HFR of 2.3 pixels is excellent for your setup',
    impact: 'high' as const,
    confidence: 95,
    data: { averageHFR: 2.3, targetHFR: 2.5 },
    recommendations: ['Continue current focusing routine', 'Consider temperature compensation'],
    trend: 'improving' as const
  },
  {
    id: 'insight2',
    type: 'weather' as const,
    title: 'Weather Pattern Analysis',
    description: 'Clear nights in March showed 15% better image quality',
    impact: 'medium' as const,
    confidence: 87,
    data: { clearNights: 8, cloudyNights: 2 },
    recommendations: ['Plan sessions during high-pressure systems'],
    trend: 'stable' as const
  }
];

const mockTrends = {
  hfrTrend: 'improving' as const,
  snrTrend: 'stable' as const,
  efficiencyTrend: 'improving' as const,
  overallTrend: 'improving' as const,
  trendData: {
    hfr: [2.8, 2.6, 2.4, 2.3, 2.2],
    snr: [38, 40, 42, 43, 44],
    efficiency: [85, 87, 88, 90, 92]
  }
};

const mockRecommendations = [
  {
    id: 'rec1',
    category: 'equipment' as const,
    priority: 'high' as const,
    title: 'Improve Guiding Performance',
    description: 'Your guide RMS could be improved with better polar alignment',
    impact: 'Reduce star trailing by 30%',
    effort: 'medium' as const,
    estimatedTime: 60,
    steps: ['Check polar alignment', 'Adjust mount', 'Test guiding'],
    relatedInsights: ['insight1']
  }
];

describe('InsightsDashboard', () => {
  let mockAnalyzer: jest.Mocked<SessionAnalyzer>;

  beforeEach(() => {
    mockAnalyzer = {
      calculateMetrics: jest.fn().mockReturnValue(mockMetrics),
      generateInsights: jest.fn().mockReturnValue(mockInsights),
      analyzeTrends: jest.fn().mockReturnValue(mockTrends),
      generateRecommendations: jest.fn().mockReturnValue(mockRecommendations),
      identifyPatterns: jest.fn().mockReturnValue({
        seasonal: {},
        weather: {},
        equipment: {},
        temporal: {}
      }),
      analyzeSession: jest.fn(),
      compareEquipment: jest.fn()
    } as any;

    MockedSessionAnalyzer.mockImplementation(() => mockAnalyzer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the dashboard with all tabs', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText('Session Analytics & Insights')).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /overview/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /insights/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /performance/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /trends/i })).toBeInTheDocument();
    });

    it('should display key metrics in overview', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText('10')).toBeInTheDocument(); // Total sessions
      expect(screen.getByText('40h')).toBeInTheDocument(); // Total imaging time
      expect(screen.getByText('90%')).toBeInTheDocument(); // Efficiency
      expect(screen.getByText('2.3')).toBeInTheDocument(); // Average HFR
    });

    it('should show loading state when no data', () => {
      render(
        <InsightsDashboard
          sessions={[]}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText(/no session data available/i)).toBeInTheDocument();
    });
  });

  describe('Overview Tab', () => {
    it('should display session statistics', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText('Total Sessions')).toBeInTheDocument();
      expect(screen.getByText('Total Imaging Time')).toBeInTheDocument();
      expect(screen.getByText('Average HFR')).toBeInTheDocument();
      expect(screen.getByText('Frame Acceptance')).toBeInTheDocument();
    });

    it('should display equipment usage chart', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText('Equipment Usage')).toBeInTheDocument();
      expect(screen.getByText('Test Setup')).toBeInTheDocument();
    });

    it('should display target type distribution', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText('Target Types')).toBeInTheDocument();
      expect(screen.getByText(/galaxy/i)).toBeInTheDocument();
      expect(screen.getByText(/nebula/i)).toBeInTheDocument();
    });

    it('should display recent session activity', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText('Recent Sessions')).toBeInTheDocument();
      expect(screen.getByText('Andromeda Galaxy')).toBeInTheDocument();
    });
  });

  describe('Insights Tab', () => {
    it('should display generated insights', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /insights/i }));

      expect(screen.getByText('Excellent Focus Performance')).toBeInTheDocument();
      expect(screen.getByText('Weather Pattern Analysis')).toBeInTheDocument();
    });

    it('should show insight details and confidence', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /insights/i }));

      expect(screen.getByText('95%')).toBeInTheDocument(); // Confidence
      expect(screen.getByText(/excellent for your setup/i)).toBeInTheDocument();
    });

    it('should filter insights by type', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /insights/i }));

      // Look for filter buttons
      const equipmentFilter = screen.getByRole('button', { name: /equipment/i });
      await user.click(equipmentFilter);

      // Should show only equipment insights
      expect(screen.getByText('Excellent Focus Performance')).toBeInTheDocument();
      expect(screen.queryByText('Weather Pattern Analysis')).not.toBeInTheDocument();
    });

    it('should show insight recommendations', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /insights/i }));

      expect(screen.getByText(/continue current focusing routine/i)).toBeInTheDocument();
      expect(screen.getByText(/consider temperature compensation/i)).toBeInTheDocument();
    });
  });

  describe('Performance Tab', () => {
    it('should display performance metrics', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /performance/i }));

      expect(screen.getByText('Image Quality Metrics')).toBeInTheDocument();
      expect(screen.getByText('Session Efficiency')).toBeInTheDocument();
      expect(screen.getByText('Equipment Performance')).toBeInTheDocument();
    });

    it('should show performance comparisons', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /performance/i }));

      expect(screen.getByText(/vs previous period/i)).toBeInTheDocument();
    });

    it('should display performance charts', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /performance/i }));

      // Look for chart containers
      expect(screen.getByText('HFR Distribution')).toBeInTheDocument();
      expect(screen.getByText('SNR Trends')).toBeInTheDocument();
    });
  });

  describe('Trends Tab', () => {
    it('should display trend analysis', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /trends/i }));

      expect(screen.getByText('Performance Trends')).toBeInTheDocument();
      expect(screen.getByText(/improving/i)).toBeInTheDocument();
    });

    it('should show trend indicators', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /trends/i }));

      // Look for trend arrows or indicators
      expect(screen.getByText('HFR Trend')).toBeInTheDocument();
      expect(screen.getByText('SNR Trend')).toBeInTheDocument();
      expect(screen.getByText('Efficiency Trend')).toBeInTheDocument();
    });

    it('should allow trend period selection', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /trends/i }));

      const periodSelect = screen.getByRole('combobox', { name: /time period/i });
      await user.click(periodSelect);
      
      expect(screen.getByText('Last 30 days')).toBeInTheDocument();
      expect(screen.getByText('Last 3 months')).toBeInTheDocument();
    });
  });

  describe('Data Filtering and Search', () => {
    it('should filter sessions by date range', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const dateFilter = screen.getByLabelText(/date range/i);
      await user.click(dateFilter);

      // Should trigger re-analysis with filtered data
      await waitFor(() => {
        expect(mockAnalyzer.calculateMetrics).toHaveBeenCalledTimes(2);
      });
    });

    it('should filter by equipment', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const equipmentFilter = screen.getByRole('combobox', { name: /equipment/i });
      await user.click(equipmentFilter);
      await user.click(screen.getByText('Test Setup'));

      await waitFor(() => {
        expect(mockAnalyzer.calculateMetrics).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              equipment: expect.objectContaining({ name: 'Test Setup' })
            })
          ])
        );
      });
    });

    it('should filter by target type', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const targetFilter = screen.getByRole('combobox', { name: /target type/i });
      await user.click(targetFilter);
      await user.click(screen.getByText('Galaxy'));

      await waitFor(() => {
        expect(mockAnalyzer.calculateMetrics).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              target: expect.objectContaining({ type: 'galaxy' })
            })
          ])
        );
      });
    });
  });

  describe('Interactive Features', () => {
    it('should show detailed view when clicking on insight', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      await user.click(screen.getByRole('tab', { name: /insights/i }));

      const insightCard = screen.getByText('Excellent Focus Performance').closest('div');
      await user.click(insightCard!);

      expect(screen.getByText(/detailed analysis/i)).toBeInTheDocument();
    });

    it('should allow exporting data', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);

      expect(screen.getByText(/export options/i)).toBeInTheDocument();
    });

    it('should refresh data when requested', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);

      await waitFor(() => {
        expect(mockAnalyzer.calculateMetrics).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle analyzer errors gracefully', () => {
      mockAnalyzer.calculateMetrics.mockImplementation(() => {
        throw new Error('Analysis failed');
      });

      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText(/error analyzing sessions/i)).toBeInTheDocument();
    });

    it('should show empty state for no insights', () => {
      mockAnalyzer.generateInsights.mockReturnValue([]);

      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText(/no insights available/i)).toBeInTheDocument();
    });

    it('should handle missing session data', () => {
      render(
        <InsightsDashboard
          sessions={[]}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByText(/no session data/i)).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should adapt layout for mobile screens', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      // Should still render main content
      expect(screen.getByText('Session Analytics & Insights')).toBeInTheDocument();
    });

    it('should show/hide details based on screen size', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      // Desktop should show detailed metrics
      expect(screen.getByText('Frame Acceptance')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /overview/i })).toHaveAttribute('aria-selected', 'true');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const overviewTab = screen.getByRole('tab', { name: /overview/i });
      const insightsTab = screen.getByRole('tab', { name: /insights/i });

      overviewTab.focus();
      await user.keyboard('{ArrowRight}');
      
      expect(insightsTab).toHaveFocus();
    });

    it('should announce data updates to screen readers', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);

      expect(screen.getByRole('status')).toHaveTextContent(/data updated/i);
    });
  });

  describe('Performance', () => {
    it('should handle large datasets efficiently', () => {
      const largeSessions = Array.from({ length: 1000 }, (_, i) => ({
        ...mockSessions[0],
        id: `session${i}`,
        date: new Date(2024, 0, 1 + i)
      }));

      const startTime = Date.now();
      
      render(
        <InsightsDashboard
          sessions={largeSessions}
          equipment={mockEquipment}
        />
      );

      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(2000); // Should render within 2 seconds
      expect(screen.getByText('Session Analytics & Insights')).toBeInTheDocument();
    });

    it('should debounce filter changes', async () => {
      const user = userEvent.setup();
      
      render(
        <InsightsDashboard
          sessions={mockSessions}
          equipment={mockEquipment}
        />
      );

      const dateFilter = screen.getByLabelText(/date range/i);
      
      // Rapid filter changes
      await user.click(dateFilter);
      await user.click(dateFilter);
      await user.click(dateFilter);

      // Should only trigger analysis once after debounce
      await waitFor(() => {
        expect(mockAnalyzer.calculateMetrics).toHaveBeenCalledTimes(2); // Initial + debounced
      });
    });
  });
});
